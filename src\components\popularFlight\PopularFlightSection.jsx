"use client";
import React from 'react';
import FlightCard from '../flightList/FlightCard';
import SectionBadge from '../ui/SectionBadge';
import ActionButton from '../ui/ActionButton';

const PopularFlightSection = ({ flights = [], regionName = "Popular Flights" }) => {
    if (!flights || flights.length === 0) {
        return (
            <section className="bg-[#F6F6F6] py-12 sm:py-16 lg:py-20">
                <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                    <div className="text-center">
                        <SectionBadge text="FLIGHTS" isMobile={false} />
                        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6">
                            {regionName} Flights
                        </h2>
                        <div className="bg-white rounded-lg shadow-sm p-12">
                            <div className="text-gray-500 text-lg mb-2">No flights available</div>
                            <p className="text-gray-400">Please check back later for flight options to this destination.</p>
                        </div>
                    </div>
                </div>
            </section>
        );
    }

    return (
        <section className="bg-[#F6F6F6] py-12 sm:py-16 lg:py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                {/* Header */}
                <div className="text-center mb-12">
                    <SectionBadge text="FLIGHTS" isMobile={false} />
                    <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#0C2C7A] dark:text-white mb-6">
                        {regionName} Flights
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                        Discover the best flight deals to {regionName}. Book now and save on your next adventure!
                    </p>
                </div>

                {/* Flight Cards */}
                <div className="space-y-4 mb-12">
                    {flights.map((flight) => (
                        <div key={flight.id} className="bg-white rounded-lg shadow-sm">
                            <FlightCard flight={flight} />
                        </div>
                    ))}
                </div>

                {/* Call to Action */}
                <div className="text-center">
                    <ActionButton text="View More Flights" isMobile={false} />
                </div>
            </div>
        </section>
    );
};

export default PopularFlightSection;
