import Booking from '@/components/home/<USER>';
import Header from '@/components/home/<USER>';
import React from 'react';



const PopularFlight = () => {
    const breadcrumbItems = [
        { label: 'Popular Flight', href: '/popularFlight' }
    ];

    return (
        <>
        <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Popular Flight"
        heroSubtitle=""
        //breadcrumbItems={breadcrumbItems}
        className="-mt-12"
      />
      
        </>
    );
};

export default PopularFlight;